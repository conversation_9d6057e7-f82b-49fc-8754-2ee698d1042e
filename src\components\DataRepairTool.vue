<template>
  <div class="data-repair-tool">
    <el-card class="main-card">
      <template #header>
        <div class="card-header">
          <span>数据修复工具</span>
        </div>
      </template>

      <!-- 标签页 -->
      <el-tabs v-model="activeTab" @tab-change="handleTabChange" class="repair-tabs">
        <el-tab-pane 
          v-for="tab in tabs" 
          :key="tab.name" 
          :label="tab.label" 
          :name="tab.name"
        >
          <!-- 标签页表单内容 -->
          <div class="tab-form">
            <component 
              :is="tab.component" 
              :form-data="tabFormData[tab.name]"
              @update:form-data="updateTabFormData(tab.name, $event)"
            />
          </div>
        </el-tab-pane>
      </el-tabs>

      <!-- 快速修复功能 -->
      <div class="repair-section">
        <el-card class="section-card">
          <template #header>
            <div class="section-header">
              <span>快速修复功能</span>
            </div>
          </template>
          <QuickRepairTable
            ref="quickRepairRef"
            :columns="quickRepairColumns"
            :data="quickRepairData"
            :thread-count="quickRepairThreadCount"
            @update:data="quickRepairData = $event"
            @update:thread-count="quickRepairThreadCount = $event"
            @start-repair="handleStartRepair('quick')"
          />
        </el-card>
      </div>

      <!-- 大批量修复功能 -->
      <div class="repair-section">
        <el-card class="section-card">
          <template #header>
            <div class="section-header">
              <span>大批量修复功能</span>
            </div>
          </template>
          <BatchRepairUpload
            ref="batchRepairRef"
            :template-columns="batchRepairColumns"
            :thread-count="batchRepairThreadCount"
            :progress="batchRepairProgress"
            @update:thread-count="batchRepairThreadCount = $event"
            @start-repair="handleStartRepair('batch')"
            @file-imported="handleFileImported"
          />
        </el-card>
      </div>

      <!-- 修复原因对话框 -->
      <el-dialog
        v-model="repairReasonDialogVisible"
        title="填写修复原因"
        width="500px"
        :close-on-click-modal="false"
      >
        <el-form :model="repairReasonForm" label-width="100px">
          <el-form-item label="修复原因" required>
            <el-input
              v-model="repairReasonForm.reason"
              type="textarea"
              :rows="3"
              placeholder="请填写修复原因，格式如:修复上架编号_linweihong_0719"
            />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="repairReasonDialogVisible = false">取消</el-button>
            <el-button type="primary" @click="confirmRepair">确认</el-button>
          </span>
        </template>
      </el-dialog>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import NodeOperationForm from './forms/NodeOperationForm.vue'
import QuickRepairTable from './repair/QuickRepairTable.vue'
import BatchRepairUpload from './repair/BatchRepairUpload.vue'
import { repairService } from '../services/repairService'

// 标签页配置
const tabs = [
  { name: 'node', label: 'node操作', component: NodeOperationForm },
  { name: 'tab2', label: '标签页2', component: NodeOperationForm },
  { name: 'tab3', label: '标签页3', component: NodeOperationForm },
  { name: 'tab4', label: '标签页4', component: NodeOperationForm },
  { name: 'tab5', label: '标签页5', component: NodeOperationForm },
  { name: 'tab6', label: '标签页6', component: NodeOperationForm },
  { name: 'tab7', label: '标签页7', component: NodeOperationForm },
  { name: 'tab8', label: '标签页8', component: NodeOperationForm },
  { name: 'tab9', label: '标签页9', component: NodeOperationForm },
  { name: 'tab10', label: '标签页10', component: NodeOperationForm }
]

// 当前活动标签页
const activeTab = ref('node')

// 各标签页表单数据
const tabFormData = reactive({
  node: {
    tableName: '',
    action: '',
    queryConditions: '',
    updateFields: '',
    deleteConditions: ''
  },
  tab2: {},
  tab3: {},
  tab4: {},
  tab5: {},
  tab6: {},
  tab7: {},
  tab8: {},
  tab9: {},
  tab10: {}
})

// 快速修复相关数据
const quickRepairData = ref([])
const quickRepairThreadCount = ref(10)
const quickRepairColumns = computed(() => {
  // 根据当前标签页返回不同的列配置
  return ['channel', 'sellerId', 'skuId', 'mskuId']
})

// 大批量修复相关数据
const batchRepairThreadCount = ref(50)
const batchRepairColumns = computed(() => {
  // 根据当前标签页返回不同的列配置
  return ['channel', 'sellerId', 'skuId', 'mskuId']
})
const batchRepairProgress = reactive({
  percentage: 0,
  current: 0,
  total: 0,
  timeElapsed: 0,
  timeRemaining: 0,
  isProcessing: false
})

// 批量修复组件引用
const batchRepairRef = ref()
const quickRepairRef = ref()

// 修复原因对话框
const repairReasonDialogVisible = ref(false)
const repairReasonForm = reactive({
  reason: ''
})
let currentRepairType = ''

// 方法
const handleTabChange = (tabName: string) => {
  // 切换标签页时清空字段选择
  console.log('切换到标签页:', tabName)
}

const updateTabFormData = (tabName: string, data: any) => {
  tabFormData[tabName] = { ...tabFormData[tabName], ...data }
}

const handleStartRepair = (type: 'quick' | 'batch') => {
  currentRepairType = type
  
  // 校验表单
  if (!validateForm()) {
    return
  }
  
  // 校验数据
  if (!validateData(type)) {
    return
  }
  
  // 显示修复原因对话框
  repairReasonDialogVisible.value = true
}

const validateForm = () => {
  const currentFormData = tabFormData[activeTab.value]

  // 使用修复服务进行验证
  const validation = repairService.validateFormData(activeTab.value, currentFormData)
  if (!validation.valid) {
    ElMessage.error(validation.message)
    return false
  }

  return true
}

const validateData = (type: 'quick' | 'batch') => {
  if (type === 'quick') {
    const requiredFields = quickRepairColumns.value
    const validation = repairService.validateData(quickRepairData.value, requiredFields)
    if (!validation.valid) {
      ElMessage.error(validation.message)
      return false
    }
  } else {
    // 批量修复的数据校验将在执行时处理
  }

  return true
}

const confirmRepair = () => {
  if (!repairReasonForm.reason.trim()) {
    ElMessage.error('请填写修复原因')
    return
  }
  
  repairReasonDialogVisible.value = false
  
  // 开始执行修复
  executeRepair(currentRepairType)
}

const executeRepair = async (type: 'quick' | 'batch') => {
  const currentFormData = tabFormData[activeTab.value]
  const reason = repairReasonForm.reason

  try {
    if (type === 'quick') {
      await executeQuickRepair(currentFormData, reason)
    } else {
      await executeBatchRepair(currentFormData, reason)
    }
  } catch (error: any) {
    ElMessage.error(error.message || '修复过程中发生错误')
    console.error('修复错误:', error)
  }
}

// 执行快速修复
const executeQuickRepair = async (formData: any, reason: string) => {
  const threadCount = quickRepairThreadCount.value

  // 进度回调
  const progressCallback = (current: number, total: number, timeElapsed: number) => {
    // 快速修复不需要显示进度条，直接更新表格
  }

  // 结果回调
  const resultCallback = (index: number, result: 'success' | 'failure', message?: string) => {
    // 更新表格中的结果列
    if (quickRepairData.value[index]) {
      quickRepairData.value[index].result = result
      quickRepairData.value[index].message = message
    }
  }

  ElMessage.info('开始快速修复...')

  const batchResult = await repairService.repairBatchData(
    activeTab.value,
    formData,
    quickRepairData.value,
    reason,
    threadCount,
    progressCallback,
    resultCallback
  )

  ElMessage.success(`快速修复完成！成功: ${batchResult.success}, 失败: ${batchResult.failure}`)
}

// 执行批量修复
const executeBatchRepair = async (formData: any, reason: string) => {
  // 获取批量修复组件中的数据
  const batchData = batchRepairRef.value?.importedData || []

  if (batchData.length === 0) {
    ElMessage.error('没有导入数据')
    return
  }

  const threadCount = batchRepairThreadCount.value
  const requiredFields = batchRepairColumns.value

  // 验证数据
  const validation = repairService.validateData(batchData, requiredFields)
  if (!validation.valid) {
    ElMessage.error(validation.message)
    return
  }

  // 重置进度
  batchRepairProgress.isProcessing = true
  batchRepairProgress.percentage = 0
  batchRepairProgress.current = 0
  batchRepairProgress.total = batchData.length
  batchRepairProgress.timeElapsed = 0
  batchRepairProgress.timeRemaining = 0

  const startTime = Date.now()

  // 进度回调
  const progressCallback = (current: number, total: number, timeElapsed: number) => {
    batchRepairProgress.current = current
    batchRepairProgress.percentage = Math.round((current / total) * 100)
    batchRepairProgress.timeElapsed = timeElapsed

    // 计算预计剩余时间
    if (current > 0) {
      const avgTimePerItem = timeElapsed / current
      const remainingItems = total - current
      batchRepairProgress.timeRemaining = avgTimePerItem * remainingItems
    }
  }

  // 结果回调
  const resultCallback = (index: number, result: 'success' | 'failure', message?: string) => {
    // 批量修复的结果会在最后统一处理
  }

  ElMessage.info('开始批量修复...')

  try {
    const batchResult = await repairService.repairBatchData(
      activeTab.value,
      formData,
      batchData,
      reason,
      threadCount,
      progressCallback,
      resultCallback
    )

    // 处理完成
    batchRepairProgress.isProcessing = false
    batchRepairProgress.percentage = 100

    const totalTime = (Date.now() - startTime) / 1000

    // 设置处理结果到批量修复组件
    if (batchRepairRef.value) {
      batchRepairRef.value.setProcessedData(batchResult.results)
      batchRepairRef.value.setTotalProcessTime(totalTime)
    }

    // 显示完成消息
    if (batchResult.failure === 0) {
      ElMessage.success(`全部处理成功！共处理 ${batchResult.success} 条数据`)
    } else if (batchResult.success === 0) {
      ElMessage.error(`全部处理失败！共处理 ${batchResult.failure} 条数据`)
    } else {
      ElMessage.warning(`部分处理失败！成功 ${batchResult.success} 条，失败 ${batchResult.failure} 条`)
    }

  } catch (error: any) {
    batchRepairProgress.isProcessing = false
    throw error
  }
}

const handleFileImported = (data: any[]) => {
  console.log('导入的文件数据:', data)
}
</script>

<style scoped>
.data-repair-tool {
  padding: 20px;
}

.main-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 18px;
  font-weight: bold;
}

.repair-tabs {
  margin-bottom: 30px;
}

.tab-form {
  padding: 20px 0;
  min-height: 120px;
}

.repair-section {
  margin-bottom: 30px;
}

.section-card {
  border: 1px solid #e4e7ed;
}

.section-header {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
