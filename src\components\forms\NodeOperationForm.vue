<template>
  <div class="node-operation-form">
    <el-form :model="localFormData" label-width="120px" :inline="true">
      <el-form-item label="表名" required>
        <el-select 
          v-model="localFormData.tableName" 
          placeholder="请选择表名"
          style="width: 200px"
          @change="handleTableNameChange"
        >
          <el-option
            v-for="table in tableOptions"
            :key="table.value"
            :label="table.label"
            :value="table.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="动作" required>
        <el-select 
          v-model="localFormData.action" 
          placeholder="请选择动作"
          style="width: 150px"
          @change="handleActionChange"
        >
          <el-option
            v-for="action in actionOptionsRef"
            :key="action.value"
            :label="action.label"
            :value="action.value"
          />
        </el-select>
      </el-form-item>

      <!-- 更新操作的额外字段 -->
      <template v-if="localFormData.action === '更新'">
        <el-form-item label="查询条件" required>
          <el-input
            v-model="localFormData.queryConditions"
            placeholder="可填写多个字段，并用逗号隔开"
            style="width: 250px"
            @input="handleFieldChange"
          />
        </el-form-item>

        <el-form-item label="更新字段" required>
          <el-input
            v-model="localFormData.updateFields"
            placeholder="可填写多个字段，并用逗号隔开"
            style="width: 250px"
            @input="handleFieldChange"
          />
        </el-form-item>
      </template>

      <!-- 删除操作的额外字段 -->
      <template v-if="localFormData.action === '删除'">
        <el-form-item label="条件删除" required>
          <el-input
            v-model="localFormData.deleteConditions"
            placeholder="根据查询字段条件删除记录行"
            style="width: 300px"
            @input="handleFieldChange"
          />
        </el-form-item>
      </template>
    </el-form>

    <!-- 表单配置说明 -->
    <div class="form-description" v-if="localFormData.tableName || localFormData.action">
      <el-alert
        :title="getFormDescription()"
        type="info"
        :closable="false"
        show-icon
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { tableNameOptions, actionOptions } from '../../config/apiConfig'

// Props
interface Props {
  formData?: {
    tableName?: string
    action?: string
    queryConditions?: string
    updateFields?: string
    deleteConditions?: string
  }
}

const props = withDefaults(defineProps<Props>(), {
  formData: () => ({})
})

// Emits
const emit = defineEmits<{
  'update:form-data': [data: any]
}>()

// 本地表单数据
const localFormData = reactive({
  tableName: '',
  action: '',
  queryConditions: '',
  updateFields: '',
  deleteConditions: '',
  ...props.formData
})

// 表名选项（从配置获取）
const tableOptions = computed(() => {
  return tableNameOptions.node || []
})

// 动作选项（从配置获取）
const actionOptionsRef = ref(actionOptions)

// 监听表单数据变化
watch(
  () => props.formData,
  (newData) => {
    Object.assign(localFormData, newData)
  },
  { deep: true }
)

// 处理表名变化
const handleTableNameChange = () => {
  // 清空其他字段
  localFormData.action = ''
  localFormData.queryConditions = ''
  localFormData.updateFields = ''
  localFormData.deleteConditions = ''
  
  emitFormData()
}

// 处理动作变化
const handleActionChange = () => {
  // 清空条件字段
  localFormData.queryConditions = ''
  localFormData.updateFields = ''
  localFormData.deleteConditions = ''
  
  emitFormData()
}

// 处理字段变化
const handleFieldChange = () => {
  emitFormData()
}

// 发送表单数据变化
const emitFormData = () => {
  emit('update:form-data', { ...localFormData })
}

// 获取表单描述
const getFormDescription = () => {
  if (!localFormData.tableName && !localFormData.action) {
    return ''
  }
  
  let description = ''
  
  if (localFormData.tableName) {
    description += `当前选择表: ${localFormData.tableName}`
  }
  
  if (localFormData.action) {
    description += description ? ` | 操作类型: ${localFormData.action}` : `操作类型: ${localFormData.action}`
    
    if (localFormData.action === '更新') {
      description += ' | 需要填写查询条件和更新字段'
    } else if (localFormData.action === '删除') {
      description += ' | 需要填写删除条件'
    }
  }
  
  return description
}

// 初始化时发送数据
emitFormData()
</script>

<style scoped>
.node-operation-form {
  padding: 10px 0;
}

.form-description {
  margin-top: 15px;
}

.el-form-item {
  margin-bottom: 15px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-input__inner) {
  border-radius: 4px;
}

:deep(.el-select .el-input__inner) {
  border-radius: 4px;
}
</style>
