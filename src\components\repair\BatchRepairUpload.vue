<template>
  <div class="batch-repair-upload">
    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-button 
          type="primary" 
          :icon="Download" 
          @click="downloadTemplate"
          size="small"
        >
          下载导入模板
        </el-button>
        
        <el-upload
          ref="uploadRef"
          :auto-upload="false"
          :show-file-list="false"
          accept=".csv"
          :on-change="handleFileChange"
          class="upload-demo"
        >
          <el-button 
            type="success" 
            :icon="Upload"
            size="small"
          >
            导入CSV文件
          </el-button>
        </el-upload>
        
        <el-button 
          v-if="processedData.length > 0"
          type="warning" 
          :icon="Download" 
          @click="exportResults"
          size="small"
        >
          导出处理结果
        </el-button>
      </div>
      
      <div class="toolbar-right">
        <span class="thread-label">线程数:</span>
        <el-input-number
          v-model="localThreadCount"
          :min="1"
          :max="200"
          size="small"
          style="width: 100px"
          @change="handleThreadCountChange"
        />
        <el-button 
          type="primary" 
          :icon="VideoPlay"
          @click="startRepair"
          :loading="progress.isProcessing"
          :disabled="!importedData.length"
          style="margin-left: 10px"
        >
          开始修复
        </el-button>
      </div>
    </div>

    <!-- 文件信息 -->
    <div v-if="fileInfo.name" class="file-info">
      <el-alert
        :title="`已导入文件: ${fileInfo.name} (${fileInfo.size} 行数据)`"
        type="success"
        :closable="false"
        show-icon
      />
    </div>

    <!-- 进度条 -->
    <div v-if="progress.isProcessing || progress.percentage > 0" class="progress-section">
      <div class="progress-info">
        <span>修复进度: {{ progress.current }} / {{ progress.total }}</span>
        <span>已用时: {{ formatTime(progress.timeElapsed) }}</span>
        <span v-if="progress.timeRemaining > 0">预计剩余: {{ formatTime(progress.timeRemaining) }}</span>
      </div>
      
      <el-progress
        :percentage="progress.percentage"
        :status="progress.isProcessing ? 'active' : (progress.percentage === 100 ? 'success' : 'normal')"
        :stroke-width="20"
        text-inside
      />
      
      <div class="progress-stats">
        <el-tag type="success">成功: {{ successCount }}</el-tag>
        <el-tag type="danger">失败: {{ failureCount }}</el-tag>
        <el-tag type="info">总计: {{ progress.total }}</el-tag>
      </div>
    </div>

    <!-- 处理结果摘要 -->
    <div v-if="processedData.length > 0 && !progress.isProcessing" class="result-summary">
      <el-alert
        :title="getResultSummary()"
        :type="getResultType()"
        :closable="false"
        show-icon
      />
      
      <div class="total-time">
        <span>总处理时长: {{ formatTime(totalProcessTime) }}</span>
      </div>
    </div>

    <!-- 数据预览 -->
    <div v-if="importedData.length > 0" class="data-preview">
      <el-card>
        <template #header>
          <span>数据预览 (前10行)</span>
        </template>
        
        <el-table
          :data="previewData"
          border
          stripe
          style="width: 100%"
          max-height="300"
        >
          <el-table-column
            v-for="column in templateColumns"
            :key="column"
            :prop="column"
            :label="column"
            :width="120"
            show-overflow-tooltip
          />
          <el-table-column
            v-if="processedData.length > 0"
            prop="result"
            label="处理结果"
            width="100"
          >
            <template #default="{ row }">
              <el-tag
                v-if="row.result"
                :type="row.result === 'success' ? 'success' : 'danger'"
                size="small"
              >
                {{ row.result === 'success' ? '成功' : '失败' }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Download, Upload, VideoPlay } from '@element-plus/icons-vue'
import Papa from 'papaparse'

// Props
interface Props {
  templateColumns?: string[]
  threadCount?: number
  progress?: {
    percentage: number
    current: number
    total: number
    timeElapsed: number
    timeRemaining: number
    isProcessing: boolean
  }
}

const props = withDefaults(defineProps<Props>(), {
  templateColumns: () => ['channel', 'sellerId', 'skuId', 'mskuId'],
  threadCount: 50,
  progress: () => ({
    percentage: 0,
    current: 0,
    total: 0,
    timeElapsed: 0,
    timeRemaining: 0,
    isProcessing: false
  })
})

// Emits
const emit = defineEmits<{
  'update:thread-count': [count: number]
  'start-repair': []
  'file-imported': [data: any[]]
}>()

// 本地数据
const localThreadCount = ref(props.threadCount)
const importedData = ref<any[]>([])
const processedData = ref<any[]>([])
const fileInfo = reactive({
  name: '',
  size: 0
})

// 上传组件引用
const uploadRef = ref()

// 进度相关
const progress = reactive({ ...props.progress })
const totalProcessTime = ref(0)

// 计算属性
const previewData = computed(() => {
  const data = processedData.value.length > 0 ? processedData.value : importedData.value
  return data.slice(0, 10)
})

const successCount = computed(() => {
  return processedData.value.filter(item => item.result === 'success').length
})

const failureCount = computed(() => {
  return processedData.value.filter(item => item.result === 'failure').length
})

// 监听props变化
watch(() => props.progress, (newProgress) => {
  Object.assign(progress, newProgress)
}, { deep: true })

watch(() => props.threadCount, (newCount) => {
  localThreadCount.value = newCount
})

// 下载模板
const downloadTemplate = () => {
  try {
    const headers = props.templateColumns.join(',')
    const sampleRow = props.templateColumns.map(() => '').join(',')
    const csvContent = [headers, sampleRow].join('\n')
    
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `导入模板_${new Date().toISOString().slice(0, 10)}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    ElMessage.success('模板下载成功')
  } catch (error) {
    ElMessage.error('模板下载失败')
    console.error('Template download error:', error)
  }
}

// 处理文件变化
const handleFileChange = (file: any) => {
  if (!file.raw) return
  
  const reader = new FileReader()
  reader.onload = (e) => {
    try {
      const csvText = e.target?.result as string
      
      Papa.parse(csvText, {
        header: true,
        skipEmptyLines: true,
        complete: (results) => {
          if (results.errors.length > 0) {
            ElMessage.error('CSV文件解析失败')
            console.error('CSV parse errors:', results.errors)
            return
          }
          
          importedData.value = results.data as any[]
          processedData.value = []
          
          fileInfo.name = file.name
          fileInfo.size = importedData.value.length
          
          emit('file-imported', importedData.value)
          ElMessage.success(`成功导入 ${importedData.value.length} 行数据`)
        },
        error: (error) => {
          ElMessage.error('文件读取失败')
          console.error('CSV parse error:', error)
        }
      })
    } catch (error) {
      ElMessage.error('文件处理失败')
      console.error('File processing error:', error)
    }
  }
  
  reader.readAsText(file.raw, 'UTF-8')
}

// 处理线程数变化
const handleThreadCountChange = (value: number) => {
  emit('update:thread-count', value)
}

// 开始修复
const startRepair = () => {
  if (importedData.value.length === 0) {
    ElMessage.warning('请先导入CSV文件')
    return
  }
  
  emit('start-repair')
}

// 导出处理结果
const exportResults = () => {
  if (processedData.value.length === 0) {
    ElMessage.warning('没有处理结果可导出')
    return
  }
  
  try {
    const allColumns = [...props.templateColumns, 'result']
    const headers = allColumns.join(',')
    const rows = processedData.value.map(row => 
      allColumns.map(col => `"${row[col] || ''}"`).join(',')
    )
    const csvContent = [headers, ...rows].join('\n')
    
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `处理结果_${new Date().toISOString().slice(0, 10)}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    ElMessage.success('结果导出成功')
  } catch (error) {
    ElMessage.error('结果导出失败')
    console.error('Export error:', error)
  }
}

// 获取结果摘要
const getResultSummary = () => {
  const total = processedData.value.length
  const success = successCount.value
  const failure = failureCount.value
  
  if (failure === 0) {
    return `全部处理成功！共处理 ${total} 条数据`
  } else if (success === 0) {
    return `全部处理失败！共处理 ${total} 条数据`
  } else {
    return `部分处理失败！成功 ${success} 条，失败 ${failure} 条，共 ${total} 条数据`
  }
}

// 获取结果类型
const getResultType = () => {
  const failure = failureCount.value
  
  if (failure === 0) {
    return 'success'
  } else if (successCount.value === 0) {
    return 'error'
  } else {
    return 'warning'
  }
}

// 格式化时间
const formatTime = (seconds: number) => {
  if (seconds < 60) {
    return `${seconds.toFixed(2)}秒`
  } else if (seconds < 3600) {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = (seconds % 60).toFixed(2)
    return `${minutes}分${remainingSeconds}秒`
  } else {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const remainingSeconds = (seconds % 60).toFixed(2)
    return `${hours}时${minutes}分${remainingSeconds}秒`
  }
}

// 暴露方法和数据给父组件
defineExpose({
  setProcessedData: (data: any[]) => {
    processedData.value = data
  },
  setTotalProcessTime: (time: number) => {
    totalProcessTime.value = time
  },
  importedData: importedData
})
</script>

<style scoped>
.batch-repair-upload {
  width: 100%;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 10px;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 10px;
}

.thread-label {
  font-size: 14px;
  color: #606266;
}

.upload-demo {
  display: inline-block;
}

.file-info {
  margin-bottom: 15px;
}

.progress-section {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  font-size: 14px;
  color: #606266;
}

.progress-stats {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

.result-summary {
  margin-bottom: 20px;
}

.total-time {
  margin-top: 10px;
  font-size: 14px;
  color: #606266;
  text-align: right;
}

.data-preview {
  margin-top: 20px;
}

:deep(.el-upload) {
  display: inline-block;
}
</style>
