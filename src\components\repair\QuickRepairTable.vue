<template>
  <div class="quick-repair-table">
    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-button 
          type="primary" 
          :icon="Plus" 
          @click="addColumn"
          size="small"
        >
          新增列
        </el-button>
        <el-button 
          type="success" 
          :icon="Download" 
          @click="exportTable"
          size="small"
        >
          导出表格
        </el-button>
        <el-button 
          type="warning" 
          :icon="Delete" 
          @click="clearTable"
          size="small"
        >
          清空表格
        </el-button>
      </div>
      
      <div class="toolbar-right">
        <span class="thread-label">线程数:</span>
        <el-input-number
          v-model="localThreadCount"
          :min="1"
          :max="100"
          size="small"
          style="width: 100px"
          @change="handleThreadCountChange"
        />
        <el-button 
          type="primary" 
          :icon="VideoPlay"
          @click="startRepair"
          :loading="isProcessing"
          style="margin-left: 10px"
        >
          开始修复
        </el-button>
      </div>
    </div>

    <!-- 表格 -->
    <div class="table-container">
      <el-table
        ref="tableRef"
        :data="localData"
        border
        stripe
        style="width: 100%"
        :header-cell-style="{ backgroundColor: '#f5f7fa', color: '#606266', fontWeight: 'bold' }"
        @paste="handlePaste"
        @keydown="handleKeydown"
        class="excel-like-table"
      >
        <!-- 动态列 -->
        <el-table-column
          v-for="(column, index) in localColumns"
          :key="column"
          :prop="column"
          :label="column"
          :width="120"
          :resizable="true"
        >
          <template #header="{ column: col }">
            <div class="editable-header">
              <el-input
                v-if="editingHeader === col.property"
                v-model="editingHeaderValue"
                size="small"
                @blur="saveHeaderEdit"
                @keyup.enter="saveHeaderEdit"
                @keyup.esc="cancelHeaderEdit"
                ref="headerInputRef"
              />
              <span 
                v-else
                @dblclick="editHeader(col.property, col.label)"
                class="header-text"
              >
                {{ col.label }}
              </span>
            </div>
          </template>
          
          <template #default="{ row, $index }">
            <el-input
              v-model="row[column]"
              size="small"
              @input="handleCellChange($index, column, $event)"
              placeholder=""
            />
          </template>
        </el-table-column>

        <!-- 结果列 -->
        <el-table-column
          v-if="showResultColumn"
          prop="result"
          label="result"
          width="150"
          :resizable="true"
        >
          <template #default="{ row }">
            <el-tag
              v-if="row.result"
              :type="row.result === 'success' ? 'success' : 'danger'"
              size="small"
            >
              {{ row.result === 'success' ? '成功' : '失败' }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 添加行按钮 -->
    <div class="add-row-container">
      <el-button 
        type="primary" 
        :icon="Plus" 
        @click="addRow"
        size="small"
        plain
      >
        新增行
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, nextTick, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Download, Delete, VideoPlay } from '@element-plus/icons-vue'

// Props
interface Props {
  columns?: string[]
  data?: any[]
  threadCount?: number
}

const props = withDefaults(defineProps<Props>(), {
  columns: () => ['channel', 'sellerId', 'skuId', 'mskuId'],
  data: () => [],
  threadCount: 10
})

// Emits
const emit = defineEmits<{
  'update:data': [data: any[]]
  'update:thread-count': [count: number]
  'start-repair': []
}>()

// 本地数据
const localColumns = ref([...props.columns])
const localData = ref([...props.data])
const localThreadCount = ref(props.threadCount)
const isProcessing = ref(false)
const showResultColumn = ref(false)

// 表头编辑相关
const editingHeader = ref('')
const editingHeaderValue = ref('')
const headerInputRef = ref()

// 表格引用
const tableRef = ref()

// 监听props变化
watch(() => props.columns, (newColumns) => {
  localColumns.value = [...newColumns]
  initializeData()
}, { deep: true })

watch(() => props.data, (newData) => {
  localData.value = [...newData]
}, { deep: true })

watch(() => props.threadCount, (newCount) => {
  localThreadCount.value = newCount
})

// 初始化数据
const initializeData = () => {
  if (localData.value.length === 0) {
    addRow()
  }
}

// 添加列
const addColumn = () => {
  ElMessageBox.prompt('请输入列名', '新增列', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputPattern: /^.+$/,
    inputErrorMessage: '列名不能为空'
  }).then(({ value }) => {
    if (localColumns.value.includes(value)) {
      ElMessage.warning('列名已存在')
      return
    }
    
    localColumns.value.push(value)
    
    // 为所有行添加新列的空值
    localData.value.forEach(row => {
      row[value] = ''
    })
    
    emitDataChange()
  }).catch(() => {
    // 用户取消
  })
}

// 添加行
const addRow = () => {
  const newRow: any = {}
  localColumns.value.forEach(column => {
    newRow[column] = ''
  })
  localData.value.push(newRow)
  emitDataChange()
}

// 清空表格
const clearTable = () => {
  ElMessageBox.confirm('确定要清空表格吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    localData.value = []
    showResultColumn.value = false
    addRow() // 保留一行空数据
    emitDataChange()
    ElMessage.success('表格已清空')
  }).catch(() => {
    // 用户取消
  })
}

// 导出表格
const exportTable = () => {
  if (localData.value.length === 0) {
    ElMessage.warning('没有数据可导出')
    return
  }

  try {
    // 简单的CSV导出
    const headers = localColumns.value.join(',')
    const rows = localData.value.map(row =>
      localColumns.value.map(col => `"${row[col] || ''}"`).join(',')
    )
    const csvContent = [headers, ...rows].join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `快速修复数据_${new Date().toISOString().slice(0, 10)}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    ElMessage.success('导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
    console.error('Export error:', error)
  }
}

// 处理粘贴事件
const handlePaste = (event: ClipboardEvent) => {
  event.preventDefault()
  
  const clipboardData = event.clipboardData
  if (!clipboardData) return
  
  const pastedText = clipboardData.getData('text')
  if (!pastedText) return
  
  // 解析粘贴的数据
  const rows = pastedText.split('\n').filter(row => row.trim())
  const parsedData = rows.map(row => {
    const cells = row.split('\t')
    const rowData: any = {}
    
    localColumns.value.forEach((column, index) => {
      rowData[column] = cells[index] || ''
    })
    
    return rowData
  })
  
  // 如果需要，扩展列
  const maxCells = Math.max(...rows.map(row => row.split('\t').length))
  while (localColumns.value.length < maxCells) {
    const newColumnName = `column${localColumns.value.length + 1}`
    localColumns.value.push(newColumnName)
  }
  
  // 更新数据
  localData.value = parsedData
  emitDataChange()
  
  ElMessage.success(`已粘贴 ${parsedData.length} 行数据`)
}

// 处理键盘事件
const handleKeydown = (event: KeyboardEvent) => {
  // 可以添加更多键盘快捷键
  if (event.ctrlKey && event.key === 'v') {
    // 粘贴事件已在handlePaste中处理
  }
}

// 编辑表头
const editHeader = (property: string, currentLabel: string) => {
  editingHeader.value = property
  editingHeaderValue.value = currentLabel
  
  nextTick(() => {
    if (headerInputRef.value) {
      headerInputRef.value.focus()
    }
  })
}

// 保存表头编辑
const saveHeaderEdit = () => {
  if (editingHeaderValue.value.trim()) {
    const oldColumn = editingHeader.value
    const newColumn = editingHeaderValue.value.trim()
    
    if (oldColumn !== newColumn) {
      // 检查新列名是否已存在
      if (localColumns.value.includes(newColumn) && newColumn !== oldColumn) {
        ElMessage.warning('列名已存在')
        cancelHeaderEdit()
        return
      }
      
      // 更新列名
      const columnIndex = localColumns.value.indexOf(oldColumn)
      if (columnIndex !== -1) {
        localColumns.value[columnIndex] = newColumn
        
        // 更新数据中的键名
        localData.value.forEach(row => {
          if (row.hasOwnProperty(oldColumn)) {
            row[newColumn] = row[oldColumn]
            delete row[oldColumn]
          }
        })
        
        emitDataChange()
      }
    }
  }
  
  cancelHeaderEdit()
}

// 取消表头编辑
const cancelHeaderEdit = () => {
  editingHeader.value = ''
  editingHeaderValue.value = ''
}

// 处理单元格变化
const handleCellChange = (rowIndex: number, column: string, value: string) => {
  localData.value[rowIndex][column] = value
  emitDataChange()
}

// 处理线程数变化
const handleThreadCountChange = (value: number) => {
  emit('update:thread-count', value)
}

// 开始修复
const startRepair = () => {
  // 验证数据
  const validData = localData.value.filter(row => {
    return localColumns.value.some(column => row[column] && row[column].trim())
  })
  
  if (validData.length === 0) {
    ElMessage.warning('请添加有效数据')
    return
  }
  
  showResultColumn.value = true
  emit('start-repair')
}

// 发送数据变化
const emitDataChange = () => {
  emit('update:data', [...localData.value])
}

// 初始化
initializeData()
</script>

<style scoped>
.quick-repair-table {
  width: 100%;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.toolbar-left {
  display: flex;
  gap: 10px;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 10px;
}

.thread-label {
  font-size: 14px;
  color: #606266;
}

.table-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
}

.excel-like-table {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.editable-header {
  width: 100%;
}

.header-text {
  cursor: pointer;
  display: block;
  width: 100%;
  padding: 2px;
}

.header-text:hover {
  background-color: rgba(64, 158, 255, 0.1);
  border-radius: 2px;
}

.add-row-container {
  margin-top: 10px;
  text-align: center;
}

:deep(.el-table__header-wrapper) {
  background-color: #f5f7fa;
}

:deep(.el-table__body-wrapper) {
  max-height: 400px;
  overflow-y: auto;
}

:deep(.el-input__inner) {
  border: none;
  padding: 5px 8px;
  font-size: 12px;
}

:deep(.el-input__inner:focus) {
  border: 1px solid #409eff;
}
</style>
