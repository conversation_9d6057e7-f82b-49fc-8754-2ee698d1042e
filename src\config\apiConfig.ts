import type { ApiConfig } from '../services/repairService'

// API配置接口
export interface TabApiConfig {
  [tabName: string]: {
    apis: ApiConfig[]
    requiredFields: string[]
    description: string
  }
}

// 默认API配置
export const defaultApiConfig: TabApiConfig = {
  node: {
    apis: [
      {
        url: '/api/node/repair',
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer your-token-here'
        }
      }
    ],
    requiredFields: ['channel', 'sellerId', 'skuId', 'mskuId'],
    description: 'Node操作相关的数据修复接口'
  },
  tab2: {
    apis: [
      {
        url: '/api/tab2/repair',
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      }
    ],
    requiredFields: ['channel', 'sellerId', 'skuId', 'mskuId'],
    description: '标签页2的数据修复接口'
  },
  tab3: {
    apis: [
      {
        url: '/api/tab3/repair',
        method: 'POST'
      }
    ],
    requiredFields: ['channel', 'sellerId', 'skuId', 'mskuId'],
    description: '标签页3的数据修复接口'
  },
  tab4: {
    apis: [
      {
        url: '/api/tab4/repair',
        method: 'POST'
      }
    ],
    requiredFields: ['channel', 'sellerId', 'skuId', 'mskuId'],
    description: '标签页4的数据修复接口'
  },
  tab5: {
    apis: [
      {
        url: '/api/tab5/repair',
        method: 'POST'
      }
    ],
    requiredFields: ['channel', 'sellerId', 'skuId', 'mskuId'],
    description: '标签页5的数据修复接口'
  },
  tab6: {
    apis: [
      {
        url: '/api/tab6/repair',
        method: 'POST'
      }
    ],
    requiredFields: ['channel', 'sellerId', 'skuId', 'mskuId'],
    description: '标签页6的数据修复接口'
  },
  tab7: {
    apis: [
      {
        url: '/api/tab7/repair',
        method: 'POST'
      }
    ],
    requiredFields: ['channel', 'sellerId', 'skuId', 'mskuId'],
    description: '标签页7的数据修复接口'
  },
  tab8: {
    apis: [
      {
        url: '/api/tab8/repair',
        method: 'POST'
      }
    ],
    requiredFields: ['channel', 'sellerId', 'skuId', 'mskuId'],
    description: '标签页8的数据修复接口'
  },
  tab9: {
    apis: [
      {
        url: '/api/tab9/repair',
        method: 'POST'
      }
    ],
    requiredFields: ['channel', 'sellerId', 'skuId', 'mskuId'],
    description: '标签页9的数据修复接口'
  },
  tab10: {
    apis: [
      {
        url: '/api/tab10/repair',
        method: 'POST'
      }
    ],
    requiredFields: ['channel', 'sellerId', 'skuId', 'mskuId'],
    description: '标签页10的数据修复接口'
  }
}

// 环境配置
export const environmentConfig = {
  development: {
    baseURL: 'http://localhost:3000',
    timeout: 30000
  },
  production: {
    baseURL: 'https://your-api-domain.com',
    timeout: 60000
  },
  test: {
    baseURL: 'http://test-api.com',
    timeout: 30000
  }
}

// 获取当前环境
export const getCurrentEnvironment = (): keyof typeof environmentConfig => {
  return (import.meta.env.MODE as keyof typeof environmentConfig) || 'development'
}

// 获取当前环境配置
export const getCurrentConfig = () => {
  const env = getCurrentEnvironment()
  return environmentConfig[env]
}

// 表名配置（可配置的下拉选项）
export const tableNameOptions = {
  node: [
    { label: 'amazon-active-listings', value: 'amazon-active-listings' },
    { label: 'amazon_active_listing_logs', value: 'amazon_active_listing_logs' },
    { label: 'pid-scu-maps', value: 'pid-scu-maps' }
  ],
  // 其他标签页可以有不同的表名选项
  tab2: [
    { label: 'table2-1', value: 'table2-1' },
    { label: 'table2-2', value: 'table2-2' }
  ]
  // ... 可以为每个标签页配置不同的表名选项
}

// 动作选项配置
export const actionOptions = [
  { label: '新增', value: '新增' },
  { label: '更新', value: '更新' },
  { label: '删除', value: '删除' }
]

// 默认列配置
export const defaultColumns = {
  node: ['channel', 'sellerId', 'skuId', 'mskuId'],
  tab2: ['channel', 'sellerId', 'skuId', 'mskuId'],
  tab3: ['channel', 'sellerId', 'skuId', 'mskuId'],
  tab4: ['channel', 'sellerId', 'skuId', 'mskuId'],
  tab5: ['channel', 'sellerId', 'skuId', 'mskuId'],
  tab6: ['channel', 'sellerId', 'skuId', 'mskuId'],
  tab7: ['channel', 'sellerId', 'skuId', 'mskuId'],
  tab8: ['channel', 'sellerId', 'skuId', 'mskuId'],
  tab9: ['channel', 'sellerId', 'skuId', 'mskuId'],
  tab10: ['channel', 'sellerId', 'skuId', 'mskuId']
}

// 线程数配置
export const threadConfig = {
  quick: {
    default: 10,
    min: 1,
    max: 100
  },
  batch: {
    default: 50,
    min: 1,
    max: 200
  }
}

// 导出配置管理类
export class ConfigManager {
  private static instance: ConfigManager
  private apiConfig: TabApiConfig = { ...defaultApiConfig }

  private constructor() {}

  static getInstance(): ConfigManager {
    if (!ConfigManager.instance) {
      ConfigManager.instance = new ConfigManager()
    }
    return ConfigManager.instance
  }

  // 获取标签页API配置
  getTabConfig(tabName: string) {
    return this.apiConfig[tabName] || this.apiConfig.node
  }

  // 更新标签页API配置
  updateTabConfig(tabName: string, config: Partial<TabApiConfig[string]>) {
    if (!this.apiConfig[tabName]) {
      this.apiConfig[tabName] = { ...this.apiConfig.node }
    }
    this.apiConfig[tabName] = { ...this.apiConfig[tabName], ...config }
  }

  // 获取所有配置
  getAllConfig() {
    return this.apiConfig
  }

  // 重置配置
  resetConfig() {
    this.apiConfig = { ...defaultApiConfig }
  }

  // 从JSON加载配置
  loadConfigFromJSON(config: TabApiConfig) {
    this.apiConfig = { ...config }
  }

  // 导出配置为JSON
  exportConfigToJSON() {
    return JSON.stringify(this.apiConfig, null, 2)
  }
}

// 创建默认实例
export const configManager = ConfigManager.getInstance()
