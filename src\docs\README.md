# 数据修复工具使用说明

## 概述

这是一个基于Vue 3 + Element Plus开发的本地数据修复工具，支持快速修复和大批量修复两种模式，具有10个可配置的标签页，每个标签页可以配置不同的表单和接口。

## 功能特性

### 1. 标签页功能
- 支持10个独立的标签页
- 每个标签页有独立的表单配置
- 标签页切换会清空字段选择
- node操作标签页包含：
  - 表名下拉框（可配置）
  - 动作下拉框（新增、更新、删除）
  - 动态字段显示（根据动作类型）

### 2. 快速修复功能
- 类似Excel的表格界面
- 支持直接粘贴Excel内容
- 可编辑表头
- 新增/删除行列功能
- 导出表格功能
- 实时结果反馈
- 默认线程数：10

### 3. 大批量修复功能
- CSV文件导入
- 模板下载功能
- 进度条显示
- 处理时长统计
- 结果导出功能
- 默认线程数：50

### 4. 修复逻辑
- 表单验证
- 数据验证
- 修复原因填写
- 并发请求处理
- 进度反馈
- 结果统计

## 安装和运行

### 环境要求
- Node.js 16+
- npm 或 yarn

### 安装依赖
```bash
npm install
```

### 运行开发服务器
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

## 配置说明

### API配置

在 `src/config/apiConfig.ts` 文件中配置各标签页的API接口：

```typescript
export const defaultApiConfig: TabApiConfig = {
  node: {
    apis: [
      {
        url: '/api/node/repair',
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer your-token-here'
        }
      }
    ],
    requiredFields: ['channel', 'sellerId', 'skuId', 'mskuId'],
    description: 'Node操作相关的数据修复接口'
  }
  // ... 其他标签页配置
}
```

### 表名配置

配置各标签页的表名选项：

```typescript
export const tableNameOptions = {
  node: [
    { label: 'amazon-active-listings', value: 'amazon-active-listings' },
    { label: 'amazon_active_listing_logs', value: 'amazon_active_listing_logs' },
    { label: 'pid-scu-maps', value: 'pid-scu-maps' }
  ]
}
```

### 环境配置

配置不同环境的API地址：

```typescript
export const environmentConfig = {
  development: {
    baseURL: 'http://localhost:3000',
    timeout: 30000
  },
  production: {
    baseURL: 'https://your-api-domain.com',
    timeout: 60000
  }
}
```

## 使用方法

### 1. 选择标签页
- 点击对应的标签页
- 填写必要的表单信息

### 2. 快速修复
1. 在快速修复区域的表格中输入数据
2. 或者从Excel复制数据直接粘贴到表格
3. 设置线程数（默认10）
4. 点击"开始修复"按钮
5. 填写修复原因
6. 查看实时结果反馈

### 3. 大批量修复
1. 下载导入模板
2. 填写模板数据
3. 导入CSV文件
4. 设置线程数（默认50）
5. 点击"开始修复"按钮
6. 填写修复原因
7. 查看进度条和处理结果
8. 导出处理结果

## 接口规范

### 请求格式

```typescript
interface RepairRequestParams {
  tableName: string        // 表名
  action: string          // 动作（新增、更新、删除）
  data: Record<string, any>  // 行数据
  queryConditions?: string   // 查询条件（更新时）
  updateFields?: string     // 更新字段（更新时）
  deleteConditions?: string // 删除条件（删除时）
  reason: string           // 修复原因
}
```

### 响应格式

```typescript
interface RepairResponse {
  success: boolean    // 是否成功
  message: string    // 响应消息
  data?: any        // 响应数据（可选）
}
```

## 扩展开发

### 添加新标签页

1. 在 `src/config/apiConfig.ts` 中添加新标签页配置
2. 在 `DataRepairTool.vue` 的 `tabs` 数组中添加新标签页
3. 如需自定义表单，创建新的表单组件

### 自定义表单组件

参考 `src/components/forms/NodeOperationForm.vue` 创建新的表单组件：

```vue
<template>
  <!-- 表单内容 -->
</template>

<script setup lang="ts">
// 组件逻辑
</script>
```

### 自定义API处理

在 `src/services/repairService.ts` 中扩展API处理逻辑：

```typescript
// 添加自定义验证
validateFormData(tabName: string, formData: any) {
  // 自定义验证逻辑
}

// 添加自定义请求处理
async repairSingleData(tabName: string, formData: any, rowData: any, reason: string) {
  // 自定义请求处理逻辑
}
```

## 注意事项

1. **修复原因**：每次修复都需要填写修复原因，格式建议："修复上架编号_用户名_日期"
2. **线程数设置**：根据服务器性能调整线程数，避免过高导致服务器压力
3. **数据验证**：确保必填字段都有值，避免无效请求
4. **错误处理**：注意查看错误信息，及时处理失败的数据
5. **结果导出**：及时导出处理结果，避免数据丢失

## 故障排除

### 常见问题

1. **接口请求失败**
   - 检查API配置是否正确
   - 确认服务器地址和端口
   - 检查网络连接

2. **数据导入失败**
   - 确认CSV文件格式正确
   - 检查文件编码（建议UTF-8）
   - 确认列名与模板一致

3. **修复进度卡住**
   - 检查网络连接
   - 降低线程数重试
   - 查看浏览器控制台错误信息

### 调试方法

1. 打开浏览器开发者工具
2. 查看Console面板的错误信息
3. 查看Network面板的网络请求
4. 检查API响应内容

## 更新日志

### v1.0.0
- 初始版本发布
- 支持10个标签页
- 快速修复和大批量修复功能
- 基础API配置和扩展能力
