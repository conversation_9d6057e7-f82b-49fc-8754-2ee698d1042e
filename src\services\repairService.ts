import axios from 'axios'

// 接口配置类型
interface ApiConfig {
  url: string
  method: 'GET' | 'POST' | 'PUT' | 'DELETE'
  headers?: Record<string, string>
}

// 修复请求参数类型
interface RepairRequestParams {
  tableName: string
  action: string
  data: Record<string, any>
  queryConditions?: string
  updateFields?: string
  deleteConditions?: string
  reason: string
}

// 修复响应类型
interface RepairResponse {
  success: boolean
  message: string
  data?: any
}

// 进度回调类型
type ProgressCallback = (current: number, total: number, timeElapsed: number) => void

// 结果回调类型
type ResultCallback = (index: number, result: 'success' | 'failure', message?: string) => void

// 修复服务类
class RepairService {
  private baseURL: string = ''
  
  // 标签页接口配置映射
  private apiConfigs: Record<string, ApiConfig[]> = {
    node: [
      {
        url: '/api/node/repair',
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      }
    ],
    tab2: [
      {
        url: '/api/tab2/repair',
        method: 'POST'
      }
    ],
    tab3: [
      {
        url: '/api/tab3/repair',
        method: 'POST'
      }
    ],
    tab4: [
      {
        url: '/api/tab4/repair',
        method: 'POST'
      }
    ],
    tab5: [
      {
        url: '/api/tab5/repair',
        method: 'POST'
      }
    ],
    tab6: [
      {
        url: '/api/tab6/repair',
        method: 'POST'
      }
    ],
    tab7: [
      {
        url: '/api/tab7/repair',
        method: 'POST'
      }
    ],
    tab8: [
      {
        url: '/api/tab8/repair',
        method: 'POST'
      }
    ],
    tab9: [
      {
        url: '/api/tab9/repair',
        method: 'POST'
      }
    ],
    tab10: [
      {
        url: '/api/tab10/repair',
        method: 'POST'
      }
    ]
  }

  constructor(baseURL?: string) {
    if (baseURL) {
      this.baseURL = baseURL
    }
  }

  // 设置基础URL
  setBaseURL(url: string) {
    this.baseURL = url
  }

  // 获取标签页的API配置
  getApiConfigs(tabName: string): ApiConfig[] {
    return this.apiConfigs[tabName] || []
  }

  // 添加或更新API配置
  setApiConfig(tabName: string, configs: ApiConfig[]) {
    this.apiConfigs[tabName] = configs
  }

  // 单个数据修复请求
  async repairSingleData(
    tabName: string,
    formData: any,
    rowData: Record<string, any>,
    reason: string
  ): Promise<RepairResponse> {
    const configs = this.getApiConfigs(tabName)
    
    if (configs.length === 0) {
      throw new Error(`未找到标签页 ${tabName} 的API配置`)
    }

    // 构建请求参数
    const requestParams: RepairRequestParams = {
      tableName: formData.tableName,
      action: formData.action,
      data: rowData,
      reason: reason
    }

    // 根据动作类型添加额外参数
    if (formData.action === '更新') {
      requestParams.queryConditions = formData.queryConditions
      requestParams.updateFields = formData.updateFields
    } else if (formData.action === '删除') {
      requestParams.deleteConditions = formData.deleteConditions
    }

    // 使用第一个API配置（可以扩展为支持多个API）
    const config = configs[0]
    const url = this.baseURL + config.url

    try {
      const response = await axios({
        method: config.method,
        url: url,
        data: requestParams,
        headers: config.headers || {}
      })

      // 根据响应判断成功或失败
      if (response.status === 200 && response.data) {
        return {
          success: true,
          message: response.data.message || '修复成功',
          data: response.data
        }
      } else {
        return {
          success: false,
          message: response.data?.message || '修复失败'
        }
      }
    } catch (error: any) {
      console.error('修复请求失败:', error)
      return {
        success: false,
        message: error.response?.data?.message || error.message || '网络请求失败'
      }
    }
  }

  // 批量修复数据（并发处理）
  async repairBatchData(
    tabName: string,
    formData: any,
    dataList: Record<string, any>[],
    reason: string,
    threadCount: number = 10,
    progressCallback?: ProgressCallback,
    resultCallback?: ResultCallback
  ): Promise<{ success: number; failure: number; results: any[] }> {
    const total = dataList.length
    let completed = 0
    let successCount = 0
    let failureCount = 0
    const results: any[] = []
    const startTime = Date.now()

    // 分组处理
    const chunks = this.chunkArray(dataList, threadCount)
    
    for (let chunkIndex = 0; chunkIndex < chunks.length; chunkIndex++) {
      const chunk = chunks[chunkIndex]
      
      // 并发处理当前组
      const chunkPromises = chunk.map(async (rowData, index) => {
        const globalIndex = chunkIndex * threadCount + index
        
        try {
          const result = await this.repairSingleData(tabName, formData, rowData, reason)
          
          const resultData = {
            ...rowData,
            result: result.success ? 'success' : 'failure',
            message: result.message
          }
          
          results[globalIndex] = resultData
          
          if (result.success) {
            successCount++
          } else {
            failureCount++
          }
          
          // 调用结果回调
          if (resultCallback) {
            resultCallback(globalIndex, result.success ? 'success' : 'failure', result.message)
          }
          
          completed++
          
          // 调用进度回调
          if (progressCallback) {
            const timeElapsed = (Date.now() - startTime) / 1000
            progressCallback(completed, total, timeElapsed)
          }
          
          return resultData
        } catch (error: any) {
          const resultData = {
            ...rowData,
            result: 'failure',
            message: error.message || '处理失败'
          }
          
          results[globalIndex] = resultData
          failureCount++
          completed++
          
          if (resultCallback) {
            resultCallback(globalIndex, 'failure', error.message)
          }
          
          if (progressCallback) {
            const timeElapsed = (Date.now() - startTime) / 1000
            progressCallback(completed, total, timeElapsed)
          }
          
          return resultData
        }
      })
      
      // 等待当前组完成
      await Promise.all(chunkPromises)
    }

    return {
      success: successCount,
      failure: failureCount,
      results: results
    }
  }

  // 数组分组工具方法
  private chunkArray<T>(array: T[], chunkSize: number): T[][] {
    const chunks: T[][] = []
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize))
    }
    return chunks
  }

  // 验证表单数据
  validateFormData(tabName: string, formData: any): { valid: boolean; message?: string } {
    if (tabName === 'node') {
      if (!formData.tableName) {
        return { valid: false, message: '请选择表名' }
      }
      
      if (!formData.action) {
        return { valid: false, message: '请选择动作' }
      }
      
      if (formData.action === '更新') {
        if (!formData.queryConditions || !formData.updateFields) {
          return { valid: false, message: '更新操作需要填写查询条件和更新字段' }
        }
      } else if (formData.action === '删除') {
        if (!formData.deleteConditions) {
          return { valid: false, message: '删除操作需要填写条件删除字段' }
        }
      }
    }
    
    // 其他标签页的验证逻辑可以在这里添加
    
    return { valid: true }
  }

  // 验证数据
  validateData(dataList: Record<string, any>[], requiredFields: string[]): { valid: boolean; message?: string } {
    if (!dataList || dataList.length === 0) {
      return { valid: false, message: '没有数据需要处理' }
    }
    
    // 检查必填字段
    for (let i = 0; i < dataList.length; i++) {
      const row = dataList[i]
      for (const field of requiredFields) {
        if (!row[field] || row[field].toString().trim() === '') {
          return { valid: false, message: `第 ${i + 1} 行的 ${field} 字段不能为空` }
        }
      }
    }
    
    return { valid: true }
  }
}

// 创建默认实例
export const repairService = new RepairService()

// 导出类型
export type { ApiConfig, RepairRequestParams, RepairResponse, ProgressCallback, ResultCallback }

// 导出服务类
export default RepairService
